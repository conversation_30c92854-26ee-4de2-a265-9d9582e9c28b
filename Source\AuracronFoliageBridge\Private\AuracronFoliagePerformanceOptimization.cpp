// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Performance Optimization Implementation
// Bridge 4.12: Foliage - Performance Optimization

#include "AuracronFoliagePerformanceOptimization.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronFoliageLOD.h"
#include "AuracronFoliageStreaming.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Performance includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "EngineUtils.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "RHICommandList.h"

// Culling includes
#include "Engine/Engine.h"
#include "SceneView.h"
#include "ConvexVolume.h"
#include "SceneManagement.h"

// GPU includes
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "RenderGraphBuilder.h"
#include "RenderGraphUtils.h"

// Performance monitoring
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "DrawDebugHelpers.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"

// =============================================================================
// FOLIAGE PERFORMANCE OPTIMIZATION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliagePerformanceOptimizationManager* UAuracronFoliagePerformanceOptimizationManager::Instance = nullptr;

UAuracronFoliagePerformanceOptimizationManager* UAuracronFoliagePerformanceOptimizationManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliagePerformanceOptimizationManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliagePerformanceOptimizationManager::Initialize(const FAuracronFoliagePerformanceOptimizationConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Performance Optimization Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize performance data
    PerformanceData = FAuracronOverallPerformanceData();
    CullingData = FAuracronCullingPerformanceData();
    BatchingData = FAuracronBatchingPerformanceData();
    GPUData = FAuracronGPUPerformanceData();

    // Initialize timers
    LastCullingUpdate = 0.0f;
    LastBatchingUpdate = 0.0f;
    LastPerformanceUpdate = 0.0f;
    LastAdaptiveUpdate = 0.0f;

    // Initialize frame tracking
    FrameTimeHistory.Empty();
    FrameTimeHistory.Reserve(60); // Store 1 second of frame times at 60 FPS
    AverageFPS = Configuration.TargetFrameRate;
    FrameCounter = 0;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Optimization Manager initialized with tier: %s, target FPS: %.1f"), 
                              *UEnum::GetValueAsString(Configuration.PerformanceTier),
                              Configuration.TargetFrameRate);
}

void UAuracronFoliagePerformanceOptimizationManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear frame history
    FrameTimeHistory.Empty();

    // Reset references
    ManagedWorld.Reset();
    LODManager.Reset();
    StreamingManager.Reset();
    InstancedManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Optimization Manager shutdown completed"));
}

bool UAuracronFoliagePerformanceOptimizationManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliagePerformanceOptimizationManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_Tick);

    // Update frame time tracking
    FrameTimeHistory.Add(DeltaTime * 1000.0f); // Convert to milliseconds
    if (FrameTimeHistory.Num() > 60)
    {
        FrameTimeHistory.RemoveAt(0);
    }
    FrameCounter++;

    // Update culling
    if (Configuration.bEnableAdvancedCulling)
    {
        LastCullingUpdate += DeltaTime;
        if (LastCullingUpdate >= Configuration.CullingUpdateInterval)
        {
            UpdateCullingInternal(DeltaTime);
            LastCullingUpdate = 0.0f;
        }
    }

    // Update batching
    if (Configuration.bEnableBatchingOptimization)
    {
        LastBatchingUpdate += DeltaTime;
        if (LastBatchingUpdate >= Configuration.BatchingUpdateInterval)
        {
            UpdateBatchingInternal(DeltaTime);
            LastBatchingUpdate = 0.0f;
        }
    }

    // Update GPU performance
    if (Configuration.bEnableGPUInstancing)
    {
        UpdateGPUPerformanceInternal(DeltaTime);
    }

    // Update adaptive performance
    if (Configuration.bEnableAdaptivePerformance)
    {
        LastAdaptiveUpdate += DeltaTime;
        if (LastAdaptiveUpdate >= 1.0f) // Update every second
        {
            float CurrentFPS = CalculateFrameTime() > 0.0f ? 1000.0f / CalculateFrameTime() : 0.0f;
            UpdateAdaptivePerformanceInternal(CurrentFPS);
            LastAdaptiveUpdate = 0.0f;
        }
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= Configuration.PerformanceMonitoringInterval)
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliagePerformanceOptimizationManager::SetConfiguration(const FAuracronFoliagePerformanceOptimizationConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Optimization configuration updated"));
}

FAuracronFoliagePerformanceOptimizationConfiguration UAuracronFoliagePerformanceOptimizationManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliagePerformanceOptimizationManager::EnableFrustumCulling(bool bEnabled)
{
    Configuration.bEnableFrustumCulling = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Frustum culling %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::UpdateFrustumCulling(const FVector& CameraLocation, const FVector& CameraDirection, float FOV)
{
    if (!Configuration.bEnableFrustumCulling)
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_FrustumCulling);

    double StartTime = FPlatformTime::Seconds();
    
    bool bCullingPerformed = PerformFrustumCulling(CameraLocation, CameraDirection, FOV);
    
    double EndTime = FPlatformTime::Seconds();
    CullingData.FrustumCullingTimeMs = (EndTime - StartTime) * 1000.0f;

    if (bCullingPerformed)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Frustum culling completed in %.2f ms"), CullingData.FrustumCullingTimeMs);
    }
}

void UAuracronFoliagePerformanceOptimizationManager::EnableOcclusionCulling(bool bEnabled)
{
    Configuration.bEnableOcclusionCulling = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Occlusion culling %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::UpdateOcclusionCulling(const FVector& CameraLocation)
{
    if (!Configuration.bEnableOcclusionCulling)
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_OcclusionCulling);

    double StartTime = FPlatformTime::Seconds();
    
    bool bCullingPerformed = PerformOcclusionCulling(CameraLocation);
    
    double EndTime = FPlatformTime::Seconds();
    CullingData.OcclusionCullingTimeMs = (EndTime - StartTime) * 1000.0f;

    if (bCullingPerformed)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Occlusion culling completed in %.2f ms"), CullingData.OcclusionCullingTimeMs);
    }
}

void UAuracronFoliagePerformanceOptimizationManager::EnableBatchingOptimization(bool bEnabled)
{
    Configuration.bEnableBatchingOptimization = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Batching optimization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::OptimizeBatches()
{
    if (!Configuration.bEnableBatchingOptimization)
    {
        return;
    }

    TRACE_CPUPROFILER_EVENT_SCOPE(AuracronFoliagePerformanceOptimization_OptimizeBatches);

    double StartTime = FPlatformTime::Seconds();
    
    OptimizeBatchesInternal();
    
    double EndTime = FPlatformTime::Seconds();
    BatchingData.BatchOptimizationTimeMs = (EndTime - StartTime) * 1000.0f;

    OnBatchOptimized.Broadcast(BatchingData.OptimizedBatches, BatchingData.BatchOptimizationTimeMs);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Batch optimization completed: %d batches optimized in %.2f ms"), 
                              BatchingData.OptimizedBatches, BatchingData.BatchOptimizationTimeMs);
}

void UAuracronFoliagePerformanceOptimizationManager::EnableGPUInstancing(bool bEnabled)
{
    Configuration.bEnableGPUInstancing = bEnabled;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("GPU instancing %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliagePerformanceOptimizationManager::SetGPUInstancingMode(EAuracronGPUInstancingMode Mode)
{
    Configuration.GPUInstancingMode = Mode;
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("GPU instancing mode set to: %s"), *UEnum::GetValueAsString(Mode));
}

EAuracronGPUInstancingMode UAuracronFoliagePerformanceOptimizationManager::GetGPUInstancingMode() const
{
    return Configuration.GPUInstancingMode;
}

void UAuracronFoliagePerformanceOptimizationManager::ValidateConfiguration()
{
    // Validate culling settings
    Configuration.FrustumCullingMargin = FMath::Max(0.0f, Configuration.FrustumCullingMargin);
    Configuration.OcclusionCullingAccuracy = FMath::Clamp(Configuration.OcclusionCullingAccuracy, 0.1f, 1.0f);
    Configuration.OcclusionQueryBudget = FMath::Max(100, Configuration.OcclusionQueryBudget);

    // Validate batching settings
    Configuration.MaxInstancesPerBatch = FMath::Max(100, Configuration.MaxInstancesPerBatch);
    Configuration.BatchingRadius = FMath::Max(100.0f, Configuration.BatchingRadius);

    // Validate GPU settings
    Configuration.MaxGPUInstances = FMath::Max(1000, Configuration.MaxGPUInstances);

    // Validate memory settings
    Configuration.MemoryBudgetMB = FMath::Max(256.0f, Configuration.MemoryBudgetMB);

    // Validate threading settings
    Configuration.WorkerThreadCount = FMath::Clamp(Configuration.WorkerThreadCount, 1, 8);

    // Validate adaptive performance settings
    Configuration.TargetFrameRate = FMath::Max(30.0f, Configuration.TargetFrameRate);
    Configuration.PerformanceThreshold = FMath::Clamp(Configuration.PerformanceThreshold, 0.5f, 0.95f);

    // Validate update intervals
    Configuration.CullingUpdateInterval = FMath::Max(0.016f, Configuration.CullingUpdateInterval); // Min 60 FPS
    Configuration.BatchingUpdateInterval = FMath::Max(0.05f, Configuration.BatchingUpdateInterval);
    Configuration.PerformanceMonitoringInterval = FMath::Max(0.5f, Configuration.PerformanceMonitoringInterval);
}

// =============================================================================
// MISSING IMPLEMENTATIONS - CULLING METHODS
// =============================================================================

bool UAuracronFoliagePerformanceOptimizationManager::IsFrustumCullingEnabled() const
{
    return Configuration.bEnableFrustumCulling;
}

int32 UAuracronFoliagePerformanceOptimizationManager::GetFrustumCulledInstanceCount() const
{
    return CullingData.FrustumCulledInstances;
}

bool UAuracronFoliagePerformanceOptimizationManager::IsOcclusionCullingEnabled() const
{
    return Configuration.bEnableOcclusionCulling;
}

int32 UAuracronFoliagePerformanceOptimizationManager::GetOcclusionCulledInstanceCount() const
{
    return CullingData.OcclusionCulledInstances;
}

// =============================================================================
// MISSING IMPLEMENTATIONS - BATCHING METHODS
// =============================================================================

void UAuracronFoliagePerformanceOptimizationManager::RebuildBatches()
{
    if (!IsValid(this))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("RebuildBatches: Invalid manager instance"));
        return;
    }

    // Reset batch data
    BatchingData.OptimizedBatches = 0;
    BatchingData.TotalBatches = 0;
    BatchingData.AverageInstancesPerBatch = 0.0f;

    // Get all hierarchical instanced static mesh components in the world
    if (UWorld* World = ManagedWorld.Get())
    {
        int32 TotalInstances = 0;
        int32 BatchCount = 0;

        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (IsValid(Actor))
            {
                TArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents;
                Actor->GetComponents<UHierarchicalInstancedStaticMeshComponent>(HISMComponents);

                for (UHierarchicalInstancedStaticMeshComponent* HISMComp : HISMComponents)
                {
                    if (IsValid(HISMComp))
                    {
                        int32 InstanceCount = HISMComp->GetInstanceCount();
                        if (InstanceCount > 0)
                        {
                            TotalInstances += InstanceCount;
                            BatchCount++;

                            // Optimize the component's batching
                            HISMComp->MarkRenderStateDirty();
                        }
                    }
                }
            }
        }

        BatchingData.TotalBatches = BatchCount;
        BatchingData.OptimizedBatches = BatchCount;
        BatchingData.AverageInstancesPerBatch = BatchCount > 0 ? static_cast<float>(TotalInstances) / static_cast<float>(BatchCount) : 0.0f;
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Rebuilt %d batches with average %.1f instances per batch"),
        BatchingData.OptimizedBatches, BatchingData.AverageInstancesPerBatch);
}

int32 UAuracronFoliagePerformanceOptimizationManager::GetOptimizedBatchCount() const
{
    return BatchingData.OptimizedBatches;
}

float UAuracronFoliagePerformanceOptimizationManager::GetAverageInstancesPerBatch() const
{
    return BatchingData.AverageInstancesPerBatch;
}

// =============================================================================
// MISSING IMPLEMENTATIONS - GPU METHODS
// =============================================================================

int32 UAuracronFoliagePerformanceOptimizationManager::GetGPUInstanceCount() const
{
    return GPUData.GPUInstances;
}

// =============================================================================
// MISSING IMPLEMENTATIONS - MEMORY METHODS
// =============================================================================

void UAuracronFoliagePerformanceOptimizationManager::OptimizeMemoryUsage()
{
    if (!IsValid(this))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("OptimizeMemoryUsage: Invalid manager instance"));
        return;
    }

    // Compress instance data
    CompressInstanceData();

    // Calculate and update memory usage
    float TotalMemoryMB = 0.0f;

    if (UWorld* World = ManagedWorld.Get())
    {
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (IsValid(Actor))
            {
                TArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents;
                Actor->GetComponents<UHierarchicalInstancedStaticMeshComponent>(HISMComponents);

                for (UHierarchicalInstancedStaticMeshComponent* HISMComp : HISMComponents)
                {
                    if (IsValid(HISMComp))
                    {
                        int32 InstanceCount = HISMComp->GetInstanceCount();
                        // Estimate memory usage: ~1KB per instance + mesh data
                        TotalMemoryMB += (InstanceCount * 0.001f);

                        // Force garbage collection on unused instances
                        HISMComp->MarkRenderStateDirty();
                    }
                }
            }
        }
    }

    // Update memory tracking
    BatchingData.MemoryUsageMB = TotalMemoryMB;
    GPUData.GPUMemoryUsageMB = TotalMemoryMB * 0.8f; // Estimate GPU portion

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Memory optimization completed. Total memory: %.2f MB"), TotalMemoryMB);
}

void UAuracronFoliagePerformanceOptimizationManager::CompressInstanceData()
{
    if (!IsValid(this))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("CompressInstanceData: Invalid manager instance"));
        return;
    }

    CompressInstanceDataInternal();

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Instance data compression completed"));
}

float UAuracronFoliagePerformanceOptimizationManager::GetMemoryUsageMB() const
{
    return PerformanceData.TotalMemoryUsageMB;
}

bool UAuracronFoliagePerformanceOptimizationManager::IsMemoryOptimal() const
{
    return PerformanceData.TotalMemoryUsageMB <= Configuration.MemoryBudgetMB;
}

// =============================================================================
// MISSING IMPLEMENTATIONS - ADAPTIVE PERFORMANCE METHODS
// =============================================================================

void UAuracronFoliagePerformanceOptimizationManager::EnableAdaptivePerformance(bool bEnabled)
{
    Configuration.bEnableAdaptivePerformance = bEnabled;

    if (bEnabled)
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Adaptive performance enabled"));
    }
    else
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Adaptive performance disabled"));
    }
}

void UAuracronFoliagePerformanceOptimizationManager::UpdateAdaptivePerformance(float CurrentFPS)
{
    if (!Configuration.bEnableAdaptivePerformance)
    {
        return;
    }

    UpdateAdaptivePerformanceInternal(CurrentFPS);
}

void UAuracronFoliagePerformanceOptimizationManager::SetPerformanceTier(EAuracronPerformanceTier Tier)
{
    PerformanceData.CurrentPerformanceTier = Tier;
    AdjustQualitySettings(Tier);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance tier set to: %s"), *UEnum::GetValueAsString(Tier));
}

EAuracronPerformanceTier UAuracronFoliagePerformanceOptimizationManager::GetCurrentPerformanceTier() const
{
    return PerformanceData.CurrentPerformanceTier;
}

// =============================================================================
// MISSING IMPLEMENTATIONS - PERFORMANCE MONITORING METHODS
// =============================================================================

FAuracronOverallPerformanceData UAuracronFoliagePerformanceOptimizationManager::GetPerformanceData() const
{
    return PerformanceData;
}

FAuracronCullingPerformanceData UAuracronFoliagePerformanceOptimizationManager::GetCullingPerformanceData() const
{
    return CullingData;
}

FAuracronBatchingPerformanceData UAuracronFoliagePerformanceOptimizationManager::GetBatchingPerformanceData() const
{
    return BatchingData;
}

FAuracronGPUPerformanceData UAuracronFoliagePerformanceOptimizationManager::GetGPUPerformanceData() const
{
    return GPUData;
}

void UAuracronFoliagePerformanceOptimizationManager::UpdatePerformanceMetrics()
{
    if (!IsValid(this))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("UpdatePerformanceMetrics: Invalid manager instance"));
        return;
    }

    UpdatePerformanceDataInternal();
}

// =============================================================================
// MISSING IMPLEMENTATIONS - INTEGRATION METHODS
// =============================================================================

void UAuracronFoliagePerformanceOptimizationManager::IntegrateWithLODSystem(UAuracronFoliageLODManager* InLODManager)
{
    if (!IsValid(InLODManager))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("IntegrateWithLODSystem: Invalid LOD manager"));
        return;
    }

    LODManager = InLODManager;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Integrated with LOD system"));
}

void UAuracronFoliagePerformanceOptimizationManager::IntegrateWithStreamingSystem(UAuracronFoliageStreamingManager* InStreamingManager)
{
    if (!IsValid(InStreamingManager))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("IntegrateWithStreamingSystem: Invalid streaming manager"));
        return;
    }

    StreamingManager = InStreamingManager;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Integrated with streaming system"));
}

void UAuracronFoliagePerformanceOptimizationManager::SynchronizeWithInstancedSystem(UAuracronFoliageInstancedManager* InInstancedManager)
{
    if (!IsValid(InInstancedManager))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("SynchronizeWithInstancedSystem: Invalid instanced manager"));
        return;
    }

    InstancedManager = InInstancedManager;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Synchronized with instanced system"));
}

// =============================================================================
// MISSING IMPLEMENTATIONS - DEBUG METHODS
// =============================================================================

void UAuracronFoliagePerformanceOptimizationManager::EnableDebugVisualization(bool bEnabled)
{
    bDebugVisualizationEnabled = bEnabled;

    if (bEnabled)
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Debug visualization enabled"));
    }
    else
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Debug visualization disabled"));
    }
}

bool UAuracronFoliagePerformanceOptimizationManager::IsDebugVisualizationEnabled() const
{
    return bDebugVisualizationEnabled;
}

void UAuracronFoliagePerformanceOptimizationManager::DrawDebugPerformanceInfo(UWorld* World) const
{
    if (!IsValid(World) || !bDebugVisualizationEnabled)
    {
        return;
    }

    // Draw performance statistics on screen
    FVector DebugLocation = FVector(0, 0, 1000);
    FString DebugText = FString::Printf(TEXT("Foliage Performance:\nFPS: %.1f\nMemory: %.1f MB\nBatches: %d\nGPU Instances: %d"),
        PerformanceData.CurrentFPS,
        PerformanceData.TotalMemoryUsageMB,
        BatchingData.OptimizedBatches,
        GPUData.GPUInstances);

    DrawDebugString(World, DebugLocation, DebugText, nullptr, FColor::Green, 0.0f);
}

void UAuracronFoliagePerformanceOptimizationManager::LogPerformanceStatistics() const
{
    AURACRON_FOLIAGE_LOG_INFO(TEXT("=== Foliage Performance Statistics ==="));
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Current FPS: %.2f"), PerformanceData.CurrentFPS);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Memory Usage: %.2f MB"), PerformanceData.TotalMemoryUsageMB);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Optimized Batches: %d"), BatchingData.OptimizedBatches);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("GPU Instances: %d"), GPUData.GPUInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Frustum Culled: %d"), CullingData.FrustumCulledInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Occlusion Culled: %d"), CullingData.OcclusionCulledInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Performance Tier: %s"), *UEnum::GetValueAsString(PerformanceData.CurrentPerformanceTier));
    AURACRON_FOLIAGE_LOG_INFO(TEXT("====================================="));
}

// =============================================================================
// MISSING IMPLEMENTATIONS - INTERNAL METHODS
// =============================================================================

void UAuracronFoliagePerformanceOptimizationManager::UpdateAdaptivePerformanceInternal(float CurrentFPS)
{
    if (!Configuration.bEnableAdaptivePerformance)
    {
        return;
    }

    // Update performance data
    PerformanceData.CurrentFPS = CurrentFPS;

    // Check if performance is below target
    if (CurrentFPS < Configuration.TargetFrameRate * Configuration.PerformanceThreshold)
    {
        // Reduce quality if needed
        if (PerformanceData.CurrentPerformanceTier != EAuracronPerformanceTier::Low)
        {
            EAuracronPerformanceTier NewTier = static_cast<EAuracronPerformanceTier>(
                static_cast<int32>(PerformanceData.CurrentPerformanceTier) + 1);
            SetPerformanceTier(NewTier);
        }
    }
    else if (CurrentFPS > Configuration.TargetFrameRate * 1.1f)
    {
        // Increase quality if performance allows
        if (PerformanceData.CurrentPerformanceTier != EAuracronPerformanceTier::Ultra)
        {
            EAuracronPerformanceTier NewTier = static_cast<EAuracronPerformanceTier>(
                static_cast<int32>(PerformanceData.CurrentPerformanceTier) - 1);
            SetPerformanceTier(NewTier);
        }
    }
}

void UAuracronFoliagePerformanceOptimizationManager::UpdatePerformanceDataInternal()
{
    // Reset counters
    PerformanceData.TotalMemoryUsageMB = 0.0f;
    int32 TotalInstances = 0;

    // Calculate memory usage and instance counts from world components
    if (UWorld* World = ManagedWorld.Get())
    {
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (IsValid(Actor))
            {
                TArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents;
                Actor->GetComponents<UHierarchicalInstancedStaticMeshComponent>(HISMComponents);

                for (UHierarchicalInstancedStaticMeshComponent* HISMComp : HISMComponents)
                {
                    if (IsValid(HISMComp))
                    {
                        int32 InstanceCount = HISMComp->GetInstanceCount();
                        TotalInstances += InstanceCount;
                        // Estimate memory usage (1KB per instance estimate)
                        PerformanceData.TotalMemoryUsageMB += InstanceCount * 0.001f;
                    }
                }
            }
        }
    }

    // Update GPU data
    GPUData.GPUInstances = FMath::Min(TotalInstances, Configuration.MaxGPUInstances);

    // Update batching data
    BatchingData.AverageInstancesPerBatch = GetAverageInstancesPerBatch();
}

void UAuracronFoliagePerformanceOptimizationManager::CompressInstanceDataInternal()
{
    // Compress instance data for memory optimization
    if (UWorld* World = ManagedWorld.Get())
    {
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (IsValid(Actor))
            {
                TArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents;
                Actor->GetComponents<UHierarchicalInstancedStaticMeshComponent>(HISMComponents);

                for (UHierarchicalInstancedStaticMeshComponent* HISMComp : HISMComponents)
                {
                    if (IsValid(HISMComp))
                    {
                        // Mark component for rebuild to apply compression
                        HISMComp->MarkRenderStateDirty();

                        // Force update of render data
                        HISMComp->UpdateBounds();
                    }
                }
            }
        }
    }
}

void UAuracronFoliagePerformanceOptimizationManager::AdjustQualitySettings(EAuracronPerformanceTier TargetTier)
{
    switch (TargetTier)
    {
        case EAuracronPerformanceTier::Low:
            Configuration.MaxInstancesPerBatch = 500;
            Configuration.MaxGPUInstances = 5000;
            Configuration.bEnableOcclusionCulling = false;
            break;

        case EAuracronPerformanceTier::Medium:
            Configuration.MaxInstancesPerBatch = 1000;
            Configuration.MaxGPUInstances = 10000;
            Configuration.bEnableOcclusionCulling = true;
            break;

        case EAuracronPerformanceTier::High:
            Configuration.MaxInstancesPerBatch = 2000;
            Configuration.MaxGPUInstances = 20000;
            Configuration.bEnableOcclusionCulling = true;
            break;

        case EAuracronPerformanceTier::Ultra:
            Configuration.MaxInstancesPerBatch = 5000;
            Configuration.MaxGPUInstances = 50000;
            Configuration.bEnableOcclusionCulling = true;
            break;
    }

    // Apply changes
    ValidateConfiguration();
}
