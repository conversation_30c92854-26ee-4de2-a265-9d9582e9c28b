/**
 * AuracronJungleCreature.h
 * 
 * Base class for jungle creatures that can be adapted by the Adaptive Jungle AI system.
 * Provides foundation for AI-driven behavior adaptation, stat modification, and
 * environmental response.
 * 
 * Uses UE 5.6 modern AI, behavior tree, and ability system frameworks.
 */

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemComponent.h"
#include "AttributeSet.h"
#include "GameplayEffect.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardData.h"
#include "AIController.h"
#include "Perception/AIPerceptionComponent.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronJungleCreature.generated.h"

// Forward declarations
class AAuracronAdaptiveJungleAI;
class UBehaviorTreeComponent;
class UBlackboardComponent;

/**
 * Jungle creature adaptation types
 */
UENUM(BlueprintType)
enum class EJungleCreatureAdaptationType : uint8
{
    None            UMETA(DisplayName = "None"),
    Behavioral      UMETA(DisplayName = "Behavioral"),
    Physical        UMETA(DisplayName = "Physical"),
    Tactical        UMETA(DisplayName = "Tactical"),
    Environmental   UMETA(DisplayName = "Environmental"),
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Defensive       UMETA(DisplayName = "Defensive")
};

/**
 * Jungle creature types
 */
UENUM(BlueprintType)
enum class EJungleCreatureType : uint8
{
    Predator        UMETA(DisplayName = "Predator"),
    Herbivore       UMETA(DisplayName = "Herbivore"),
    Scavenger       UMETA(DisplayName = "Scavenger"),
    Guardian        UMETA(DisplayName = "Guardian"),
    Elemental       UMETA(DisplayName = "Elemental"),
    Hybrid          UMETA(DisplayName = "Hybrid")
};

/**
 * Creature behavior states
 */
UENUM(BlueprintType)
enum class ECreatureBehaviorState : uint8
{
    Idle            UMETA(DisplayName = "Idle"),
    Patrolling      UMETA(DisplayName = "Patrolling"),
    Hunting         UMETA(DisplayName = "Hunting"),
    Fleeing         UMETA(DisplayName = "Fleeing"),
    Investigating   UMETA(DisplayName = "Investigating"),
    Territorial     UMETA(DisplayName = "Territorial"),
    Social          UMETA(DisplayName = "Social"),
    Adapted         UMETA(DisplayName = "Adapted")
};

/**
 * Creature adaptation status
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronCreatureAdaptationStatus
{
    GENERATED_BODY()

    /** Whether creature is currently adapted */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    bool bIsAdapted;

    /** Current adaptation type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    EJungleCreatureAdaptationType CurrentAdaptationType;

    /** Adaptation strength */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float AdaptationStrength;

    /** Time when adaptation was applied */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float AdaptationStartTime;

    /** Duration of current adaptation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float AdaptationDuration;

    /** Target player for adaptation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    TObjectPtr<APawn> TargetPlayer;

    FAuracronCreatureAdaptationStatus()
    {
        bIsAdapted = false;
        CurrentAdaptationType = EJungleCreatureAdaptationType::Behavioral;
        AdaptationStrength = 1.0f;
        AdaptationStartTime = 0.0f;
        AdaptationDuration = 0.0f;
        TargetPlayer = nullptr;
    }
};

/**
 * Auracron Jungle Creature
 * 
 * Base class for all jungle creatures that can be adapted by the AI system.
 * Provides foundation for behavior adaptation, stat modification, and AI control.
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronJungleCreature : public ACharacter, public IAbilitySystemInterface
{
    GENERATED_BODY()

public:
    AAuracronJungleCreature();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

    // IAbilitySystemInterface
    virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;

public:
    // === Core Creature Management ===
    
    /** Get creature type */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Jungle Creature")
    EJungleCreatureType GetCreatureType() const;

    /** Set creature type */
    UFUNCTION(BlueprintCallable, Category = "Jungle Creature")
    void SetCreatureType(EJungleCreatureType NewType);

    /** Get current behavior state */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Jungle Creature")
    ECreatureBehaviorState GetBehaviorState() const;

    /** Set behavior state */
    UFUNCTION(BlueprintCallable, Category = "Jungle Creature")
    void SetBehaviorState(ECreatureBehaviorState NewState);

    /** Get adaptation status */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Jungle Creature")
    FAuracronCreatureAdaptationStatus GetAdaptationStatus() const;

    /** Check if creature is adapted */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Jungle Creature")
    bool IsCreatureAdapted() const;

    // === AI Integration ===
    
    /** Register with adaptive AI system */
    UFUNCTION(BlueprintCallable, Category = "AI Integration")
    void RegisterWithAdaptiveAI(AAuracronAdaptiveJungleAI* AISystem);

    /** Unregister from adaptive AI system */
    UFUNCTION(BlueprintCallable, Category = "AI Integration")
    void UnregisterFromAdaptiveAI();

    /** Apply AI adaptation */
    UFUNCTION(BlueprintCallable, Category = "AI Integration")
    void ApplyAIAdaptation(const FAuracronCreatureAdaptation& Adaptation);

    /** Remove AI adaptation */
    UFUNCTION(BlueprintCallable, Category = "AI Integration")
    void RemoveAIAdaptation();

    // === Behavior Management ===
    
    /** Update creature behavior */
    UFUNCTION(BlueprintCallable, Category = "Behavior")
    void UpdateCreatureBehavior(float DeltaTime);

    /** React to player presence */
    UFUNCTION(BlueprintCallable, Category = "Behavior")
    void ReactToPlayerPresence(APawn* Player);

    /** Update territorial behavior */
    UFUNCTION(BlueprintCallable, Category = "Behavior")
    void UpdateTerritorialBehavior();

    /** Update social behavior */
    UFUNCTION(BlueprintCallable, Category = "Behavior")
    void UpdateSocialBehavior();

    // === Configuration ===
    
    /** Creature type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    EJungleCreatureType CreatureType;

    /** Base behavior tree */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    TObjectPtr<UBehaviorTree> BaseBehaviorTree;

    /** Blackboard data */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    TObjectPtr<UBlackboardData> CreatureBlackboard;

    /** Base movement speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    float BaseMovementSpeed;

    /** Current movement speed (can be modified by adaptations) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    float MovementSpeed;

    /** Attack damage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    float AttackDamage;

    /** Current health */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    float Health;

    /** Territorial radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    float TerritorialRadius;

    /** Detection range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    float DetectionRange;

    /** Enable AI adaptation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Configuration")
    bool bEnableAIAdaptation;

    // === Events ===
    
    /** Called when creature is adapted */
    UFUNCTION(BlueprintImplementableEvent, Category = "Creature Events")
    void OnCreatureAdapted(EJungleCreatureAdaptationType AdaptationType);

    /** Called when adaptation is removed */
    UFUNCTION(BlueprintImplementableEvent, Category = "Creature Events")
    void OnAdaptationRemoved();

    /** Called when player is detected */
    UFUNCTION(BlueprintImplementableEvent, Category = "Creature Events")
    void OnPlayerDetected(APawn* Player);

protected:
    // === Core Components ===
    
    /** Ability system component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** Attribute set */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAttributeSet> AttributeSet;

    /** Audio component for creature sounds */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAudioComponent> CreatureAudio;

    /** VFX component for creature effects */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UNiagaraComponent> CreatureVFX;

    // === State ===
    
    /** Current behavior state */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature State")
    ECreatureBehaviorState CurrentBehaviorState;

    /** Current adaptation status */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature State")
    FAuracronCreatureAdaptationStatus AdaptationStatus;

    /** Registered AI system */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature State")
    TObjectPtr<AAuracronAdaptiveJungleAI> RegisteredAISystem;

    /** Last behavior update time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature State")
    float LastBehaviorUpdateTime;

    /** Home location */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature State")
    FVector HomeLocation;

    /** Current target player */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature State")
    TObjectPtr<APawn> CurrentTargetPlayer;

private:
    // === Implementation Methods ===
    void InitializeCreatureAI();
    void SetupCreaturePerception();
    void ConfigureCreatureAudio();
    void SetupCreatureVFX();
    void UpdateAdaptationStatus(float DeltaTime);
    void ProcessBehaviorTransition(ECreatureBehaviorState NewState);
    void ReconfigureForType();

    // === Behavior Methods ===
    TArray<APawn*> GetNearbyPlayers(float Range);
    APawn* GetClosestPlayer(const TArray<APawn*>& Players);
    TArray<AAuracronJungleCreature*> GetNearbyCreatures(float Range);
    void UpdatePatrolBehavior(float DeltaTime);
    void UpdateHuntingBehavior(float DeltaTime);
    void UpdateFleeingBehavior(float DeltaTime);
    void UpdateInvestigatingBehavior(float DeltaTime);
    ECreatureBehaviorState DetermineReactionBehavior(APawn* Player);
    
    // === State Tracking ===
    bool bIsInitialized;
    float LastAdaptationTime;
    FTimerHandle BehaviorUpdateTimer;
};
