#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "Components/PrimitiveComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Camera/CameraComponent.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronRealmTransitionComponent.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class UAuracronLayerComponent;

/**
 * Transition effect configuration
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronTransitionEffects
{
    GENERATED_BODY()

    /** Visual effect for transition start */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    TObjectPtr<UNiagaraSystem> StartEffect;

    /** Visual effect during transition */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    TObjectPtr<UNiagaraSystem> TransitionEffect;

    /** Visual effect for transition end */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    TObjectPtr<UNiagaraSystem> EndEffect;

    /** Audio for transition start */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    TObjectPtr<USoundBase> StartSound;

    /** Audio during transition */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    TObjectPtr<USoundBase> TransitionSound;

    /** Audio for transition end */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    TObjectPtr<USoundBase> EndSound;

    /** Screen effect intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    float ScreenEffectIntensity;

    /** Camera shake intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Effects")
    float CameraShakeIntensity;

    FAuracronTransitionEffects()
    {
        ScreenEffectIntensity = 0.5f;
        CameraShakeIntensity = 0.3f;
    }
};

/**
 * Transition path data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronTransitionPath
{
    GENERATED_BODY()

    /** Source layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    EAuracronRealmLayer SourceLayer;

    /** Target layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    EAuracronRealmLayer TargetLayer;

    /** Transition type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    ERealmTransitionType TransitionType;

    /** Path waypoints */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    TArray<FVector> Waypoints;

    /** Transition duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    float Duration;

    /** Transition curve */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    TObjectPtr<UCurveFloat> TransitionCurve;

    /** Effects configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Path")
    FAuracronTransitionEffects Effects;

    FAuracronTransitionPath()
    {
        SourceLayer = EAuracronRealmLayer::None;
        TargetLayer = EAuracronRealmLayer::None;
        TransitionType = ERealmTransitionType::Gradual;
        Duration = 2.0f;
    }
};

/**
 * Auracron Realm Transition Component
 * 
 * Handles smooth transitions between realm layers:
 * - Manages transition animations and effects
 * - Controls camera movement during transitions
 * - Applies layer-specific physics changes
 * - Handles collision and interaction updates
 * - Optimizes performance during transitions
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Auracron), meta=(BlueprintSpawnableComponent))
class AURACRONDYNAMICREALMBRIDGE_API UAuracronRealmTransitionComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronRealmTransitionComponent();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
    // Transition control
    UFUNCTION(BlueprintCallable, Category = "Realm Transition")
    bool StartTransition(EAuracronRealmLayer TargetLayer, ERealmTransitionType TransitionType = ERealmTransitionType::Gradual);

    UFUNCTION(BlueprintCallable, Category = "Realm Transition")
    void CancelTransition();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Realm Transition")
    bool IsTransitioning() const { return bIsTransitioning; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Realm Transition")
    float GetTransitionProgress() const { return TransitionProgress; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Realm Transition")
    EAuracronRealmLayer GetTransitionTarget() const { return TransitionTargetLayer; }

    // Transition configuration
    UFUNCTION(BlueprintCallable, Category = "Transition Configuration")
    void SetTransitionPath(const FAuracronTransitionPath& NewPath);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Transition Configuration")
    FAuracronTransitionPath GetTransitionPath() const { return CurrentTransitionPath; }

    UFUNCTION(BlueprintCallable, Category = "Transition Configuration")
    void SetTransitionEffects(const FAuracronTransitionEffects& NewEffects);

    // Effect management
    UFUNCTION(BlueprintCallable, Category = "Transition Effects")
    void PlayTransitionEffects();

    UFUNCTION(BlueprintCallable, Category = "Transition Effects")
    void StopTransitionEffects();

    UFUNCTION(BlueprintCallable, Category = "Transition Effects")
    void UpdateTransitionEffects(float Progress);

    // Camera and view management
    UFUNCTION(BlueprintCallable, Category = "Transition Camera")
    void SetupTransitionCamera();

    UFUNCTION(BlueprintCallable, Category = "Transition Camera")
    void UpdateTransitionCamera(float Progress);

    UFUNCTION(BlueprintCallable, Category = "Transition Camera")
    void RestoreOriginalCamera();

    // Physics and collision
    UFUNCTION(BlueprintCallable, Category = "Transition Physics")
    void UpdateTransitionPhysics(float Progress);

    UFUNCTION(BlueprintCallable, Category = "Transition Physics")
    void ApplyLayerPhysics(EAuracronRealmLayer Layer);

    // Events
    UFUNCTION(BlueprintImplementableEvent, Category = "Transition Events")
    void OnTransitionStarted(EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer);

    UFUNCTION(BlueprintImplementableEvent, Category = "Transition Events")
    void OnTransitionProgress(float Progress);

    UFUNCTION(BlueprintImplementableEvent, Category = "Transition Events")
    void OnTransitionCompleted(EAuracronRealmLayer NewLayer);

    UFUNCTION(BlueprintImplementableEvent, Category = "Transition Events")
    void OnTransitionCancelled();

protected:
    // Transition configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Configuration")
    TMap<ERealmTransitionType, FAuracronTransitionPath> TransitionPaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Configuration")
    bool bAutoConfigureTransitions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Configuration")
    float DefaultTransitionDuration;

    // Current transition state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    bool bIsTransitioning;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    EAuracronRealmLayer TransitionSourceLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    EAuracronRealmLayer TransitionTargetLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    ERealmTransitionType CurrentTransitionType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    float TransitionProgress;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    float TransitionStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    FAuracronTransitionPath CurrentTransitionPath;

    // Effect components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Effects")
    TArray<TObjectPtr<UNiagaraComponent>> EffectComponents;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Effects")
    TArray<TObjectPtr<UAudioComponent>> AudioComponents;

    // Camera data
    UPROPERTY()
    FVector OriginalCameraLocation;

    UPROPERTY()
    FRotator OriginalCameraRotation;

    UPROPERTY()
    float OriginalCameraFOV;

private:
    // Internal transition logic
    void UpdateTransitionMovement(float DeltaTime);
    void UpdateTransitionAudio(float DeltaTime);
    
    // Transition type handlers
    void HandleInstantTransition();
    void HandleGradualTransition(float DeltaTime);
    void HandleCinematicTransition(float DeltaTime);
    void HandleCombatTransition(float DeltaTime);
    void HandleStealthTransition(float DeltaTime);
    
    // Effect management
    void SpawnTransitionEffects();
    void DespawnTransitionEffects();
    void ConfigureEffectsForLayer(EAuracronRealmLayer Layer);
    
    // Utility functions
    FVector CalculateTransitionPosition(float Progress) const;
    FRotator CalculateTransitionRotation(float Progress) const;
    float CalculateTransitionAlpha(float Progress) const;
    bool ValidateTransitionRequest(EAuracronRealmLayer TargetLayer) const;
    
    // Cached references
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;

    UPROPERTY()
    TObjectPtr<UAuracronLayerComponent> CachedLayerComponent;

    UPROPERTY()
    TObjectPtr<APawn> OwnerPawn;

    UPROPERTY()
    TObjectPtr<APlayerController> OwnerController;
};
