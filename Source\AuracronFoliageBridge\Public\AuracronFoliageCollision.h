// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Collision Integration System Header
// Bridge 4.7: Foliage - Collision Integration

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageLOD.h"
#include "AuracronFoliageWind.h"
// PCG integration will be added when PCG collision system is available

// Forward declarations - Production Ready
struct FBodyInstance;

// UE5.6 Collision includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"

// Destructible includes
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math and Utilities
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Misc/DateTime.h"

#include "AuracronFoliageCollision.generated.h"

// Forward declarations
class UAuracronFoliageCollisionManager;
class UAuracronFoliageBiomeManager;

// =============================================================================
// COLLISION TYPES AND ENUMS
// =============================================================================

// Collision types for foliage
UENUM(BlueprintType)
enum class EAuracronFoliageCollisionType : uint8
{
    NoCollision             UMETA(DisplayName = "No Collision"),
    QueryOnly               UMETA(DisplayName = "Query Only"),
    PhysicsOnly             UMETA(DisplayName = "Physics Only"),
    CollisionEnabled        UMETA(DisplayName = "Collision Enabled"),
    Destructible            UMETA(DisplayName = "Destructible"),
    Interactive             UMETA(DisplayName = "Interactive"),
    Trampling               UMETA(DisplayName = "Trampling"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Physics interaction types
UENUM(BlueprintType)
enum class EAuracronPhysicsInteractionType : uint8
{
    Static                  UMETA(DisplayName = "Static"),
    Kinematic               UMETA(DisplayName = "Kinematic"),
    Simulated               UMETA(DisplayName = "Simulated Physics"),
    Destructible            UMETA(DisplayName = "Destructible"),
    Trampling               UMETA(DisplayName = "Trampling Effect"),
    WindInteraction         UMETA(DisplayName = "Wind Interaction"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Destructible behavior types
UENUM(BlueprintType)
enum class EAuracronDestructibleBehavior : uint8
{
    None                    UMETA(DisplayName = "None"),
    Break                   UMETA(DisplayName = "Break"),
    Shatter                 UMETA(DisplayName = "Shatter"),
    Crumble                 UMETA(DisplayName = "Crumble"),
    Burn                    UMETA(DisplayName = "Burn"),
    Dissolve                UMETA(DisplayName = "Dissolve"),
    Explode                 UMETA(DisplayName = "Explode"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Trampling effect types
UENUM(BlueprintType)
enum class EAuracronTramplingEffect : uint8
{
    None                    UMETA(DisplayName = "None"),
    Bend                    UMETA(DisplayName = "Bend"),
    Flatten                 UMETA(DisplayName = "Flatten"),
    Crush                   UMETA(DisplayName = "Crush"),
    Damage                  UMETA(DisplayName = "Damage"),
    Destroy                 UMETA(DisplayName = "Destroy"),
    Recover                 UMETA(DisplayName = "Recover Over Time"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// COLLISION CONFIGURATION DATA
// =============================================================================

/**
 * Foliage Collision Configuration
 * Configuration for foliage collision system behavior
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageCollisionConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision System")
    bool bEnableCollisionSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision System")
    EAuracronFoliageCollisionType DefaultCollisionType = EAuracronFoliageCollisionType::CollisionEnabled;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision System")
    EAuracronPhysicsInteractionType DefaultPhysicsType = EAuracronPhysicsInteractionType::Static;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Channels")
    TEnumAsByte<ECollisionChannel> FoliageCollisionChannel = ECC_WorldStatic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Channels")
    TEnumAsByte<ECollisionChannel> PlayerCollisionChannel = ECC_Pawn;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Channels")
    TEnumAsByte<ECollisionChannel> ProjectileCollisionChannel = ECC_WorldDynamic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnablePhysicsInteraction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float DefaultMass = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float DefaultLinearDamping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float DefaultAngularDamping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float DefaultRestitution = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float DefaultFriction = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    bool bEnableDestructibleFoliage = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    EAuracronDestructibleBehavior DefaultDestructibleBehavior = EAuracronDestructibleBehavior::Break;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float DestructionThreshold = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float DestructionImpulse = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    bool bEnableTramplingEffects = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    EAuracronTramplingEffect DefaultTramplingEffect = EAuracronTramplingEffect::Bend;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    float TramplingRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    float TramplingForce = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    float TramplingRecoveryTime = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncCollisionUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxCollisionUpdatesPerFrame = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CollisionUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxCollisionDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bUseLODBasedCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bDisableCollisionForDistantFoliage = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float CollisionDisableDistance = 2000.0f;

    FAuracronFoliageCollisionConfiguration()
    {
        bEnableCollisionSystem = true;
        DefaultCollisionType = EAuracronFoliageCollisionType::CollisionEnabled;
        DefaultPhysicsType = EAuracronPhysicsInteractionType::Static;
        FoliageCollisionChannel = ECC_WorldStatic;
        PlayerCollisionChannel = ECC_Pawn;
        ProjectileCollisionChannel = ECC_WorldDynamic;
        bEnablePhysicsInteraction = true;
        DefaultMass = 1.0f;
        DefaultLinearDamping = 0.1f;
        DefaultAngularDamping = 0.1f;
        DefaultRestitution = 0.3f;
        DefaultFriction = 0.7f;
        bEnableDestructibleFoliage = true;
        DefaultDestructibleBehavior = EAuracronDestructibleBehavior::Break;
        DestructionThreshold = 100.0f;
        DestructionImpulse = 500.0f;
        bEnableTramplingEffects = true;
        DefaultTramplingEffect = EAuracronTramplingEffect::Bend;
        TramplingRadius = 100.0f;
        TramplingForce = 50.0f;
        TramplingRecoveryTime = 5.0f;
        bEnableAsyncCollisionUpdates = true;
        MaxCollisionUpdatesPerFrame = 100;
        CollisionUpdateInterval = 0.1f;
        MaxCollisionDistance = 5000.0f;
        bUseLODBasedCollision = true;
        bDisableCollisionForDistantFoliage = true;
        CollisionDisableDistance = 2000.0f;
    }
};

// =============================================================================
// COLLISION MESH DATA
// =============================================================================

/**
 * Collision Mesh Data
 * Data for collision mesh generation and management
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronCollisionMeshData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Mesh")
    FString CollisionMeshId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Mesh")
    TSoftObjectPtr<UStaticMesh> SourceMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Mesh")
    TSoftObjectPtr<UStaticMesh> CollisionMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Mesh")
    EAuracronFoliageCollisionType CollisionType = EAuracronFoliageCollisionType::CollisionEnabled;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Properties")
    float CollisionComplexity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Properties")
    int32 MaxCollisionTriangles = 500;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Properties")
    bool bUseComplexCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Properties")
    bool bGenerateOverlapEvents = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Culling")
    bool bCollisionDisabledForDistance = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Culling")
    FDateTime LastDistanceCheck;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics Properties")
    float Mass = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics Properties")
    float LinearDamping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics Properties")
    float AngularDamping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics Properties")
    float Restitution = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics Properties")
    float Friction = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsGenerated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime GenerationTime;

    FAuracronCollisionMeshData()
    {
        CollisionType = EAuracronFoliageCollisionType::CollisionEnabled;
        CollisionComplexity = 0.5f;
        MaxCollisionTriangles = 500;
        bUseComplexCollision = false;
        bGenerateOverlapEvents = true;
        Mass = 1.0f;
        LinearDamping = 0.1f;
        AngularDamping = 0.1f;
        Restitution = 0.3f;
        Friction = 0.7f;
        bIsGenerated = false;
        GenerationTime = FDateTime::Now();
    }
};

/**
 * Physics properties for foliage collision
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronPhysicsProperties
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Mass = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float LinearDamping = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float AngularDamping = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Friction = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Restitution = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnableGravity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bSimulatePhysics = false;
};

/**
 * Destructible data for foliage
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronDestructibleData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    FString DestructibleId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float Health = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float DamageThreshold = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    bool bCanBeDestroyed = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    TArray<TSoftObjectPtr<UStaticMesh>> DestructionFragments;
};

// =============================================================================
// DESTRUCTIBLE FOLIAGE DATA
// =============================================================================

/**
 * Destructible Foliage Data
 * Data for destructible foliage behavior
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronDestructibleFoliageData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    FString DestructibleId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    EAuracronDestructibleBehavior DestructibleBehavior = EAuracronDestructibleBehavior::Break;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float Health = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float MaxHealth = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float DestructionThreshold = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Destructible")
    float DestructionImpulse = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragments")
    int32 FragmentCount = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragments")
    float FragmentLifetime = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragments")
    float FragmentSpread = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TArray<FString> DestructionEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TArray<FString> DestructionSounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    bool bCanRegenerate = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    float RegenerationTime = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    float RegenerationRate = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsDestroyed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime DestructionTime;

    FAuracronDestructibleFoliageData()
    {
        DestructibleBehavior = EAuracronDestructibleBehavior::Break;
        Health = 100.0f;
        MaxHealth = 100.0f;
        DestructionThreshold = 50.0f;
        DestructionImpulse = 500.0f;
        FragmentCount = 5;
        FragmentLifetime = 10.0f;
        FragmentSpread = 200.0f;
        bCanRegenerate = false;
        RegenerationTime = 30.0f;
        RegenerationRate = 1.0f;
        bIsDestroyed = false;
        DestructionTime = FDateTime::Now();
    }
};

// =============================================================================
// TRAMPLING EFFECT DATA
// =============================================================================

/**
 * Trampling Effect Data
 * Data for trampling effects on foliage
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronTramplingEffectData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    FString TramplingId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    EAuracronTramplingEffect TramplingEffect = EAuracronTramplingEffect::Bend;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    FVector TramplingLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    float TramplingRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    float TramplingForce = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling")
    float TramplingIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    float RecoveryTime = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    float RecoveryRate = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    bool bAutoRecover = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
    float BendAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
    float FlattenAmount = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
    FVector DeformationDirection = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TArray<FString> TramplingEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TArray<FString> TramplingSounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float CurrentRecoveryProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime TramplingTime;

    FAuracronTramplingEffectData()
    {
        TramplingEffect = EAuracronTramplingEffect::Bend;
        TramplingLocation = FVector::ZeroVector;
        TramplingRadius = 100.0f;
        TramplingForce = 50.0f;
        TramplingIntensity = 1.0f;
        RecoveryTime = 5.0f;
        RecoveryRate = 0.2f;
        bAutoRecover = true;
        BendAngle = 45.0f;
        FlattenAmount = 0.5f;
        DeformationDirection = FVector::ZeroVector;
        bIsActive = false;
        CurrentRecoveryProgress = 0.0f;
        TramplingTime = FDateTime::Now();
    }
};

// =============================================================================
// COLLISION PERFORMANCE DATA
// =============================================================================

/**
 * Collision Performance Data
 * Performance metrics for collision system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronCollisionPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalCollisionMeshes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ActiveCollisionMeshes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 DestructibleInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TramplingEffects = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CollisionUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float PhysicsUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float DestructionUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float TramplingUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CollisionQueries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 PhysicsInteractions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    FAuracronCollisionPerformanceData()
    {
        TotalCollisionMeshes = 0;
        ActiveCollisionMeshes = 0;
        DestructibleInstances = 0;
        TramplingEffects = 0;
        CollisionUpdateTime = 0.0f;
        PhysicsUpdateTime = 0.0f;
        DestructionUpdateTime = 0.0f;
        TramplingUpdateTime = 0.0f;
        CollisionQueries = 0;
        PhysicsInteractions = 0;
        MemoryUsageMB = 0.0f;
    }
};

// =============================================================================
// FOLIAGE COLLISION MANAGER
// =============================================================================

/**
 * Foliage Collision Manager
 * Manager for the foliage collision system including collision mesh generation, physics interaction, and destructible foliage
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageCollisionManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    static UAuracronFoliageCollisionManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void Initialize(const FAuracronFoliageCollisionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void SetConfiguration(const FAuracronFoliageCollisionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronFoliageCollisionConfiguration GetConfiguration() const;

    // Collision mesh generation
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FString GenerateCollisionMesh(UStaticMesh* SourceMesh, EAuracronFoliageCollisionType CollisionType);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool UpdateCollisionMesh(const FString& CollisionMeshId, const FAuracronCollisionMeshData& MeshData);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool RemoveCollisionMesh(const FString& CollisionMeshId);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronCollisionMeshData GetCollisionMesh(const FString& CollisionMeshId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronCollisionMeshData> GetAllCollisionMeshes() const;

    // Physics interaction
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void SetFoliagePhysicsType(const FString& FoliageTypeId, EAuracronPhysicsInteractionType PhysicsType);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    EAuracronPhysicsInteractionType GetFoliagePhysicsType(const FString& FoliageTypeId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void ApplyPhysicsToFoliageInstance(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronCollisionMeshData& CollisionData);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void EnablePhysicsForFoliageType(const FString& FoliageTypeId, bool bEnabled);

    // Destructible foliage
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FString CreateDestructibleFoliage(const FString& FoliageInstanceId, const FAuracronDestructibleFoliageData& DestructibleData);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool UpdateDestructibleFoliage(const FString& DestructibleId, const FAuracronDestructibleFoliageData& DestructibleData);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool DestroyFoliageInstance(const FString& DestructibleId, float DamageAmount, const FVector& ImpactLocation, const FVector& ImpactDirection);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool RegenerateFoliageInstance(const FString& DestructibleId);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronDestructibleFoliageData GetDestructibleFoliage(const FString& DestructibleId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronDestructibleFoliageData> GetAllDestructibleFoliage() const;

    // Trampling effects
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FString CreateTramplingEffect(const FVector& Location, float Radius, float Force, EAuracronTramplingEffect EffectType);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool UpdateTramplingEffect(const FString& TramplingId, const FAuracronTramplingEffectData& TramplingData);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool RemoveTramplingEffect(const FString& TramplingId);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void ApplyTramplingToArea(const FVector& Location, float Radius, float Force, EAuracronTramplingEffect EffectType);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronTramplingEffectData GetTramplingEffect(const FString& TramplingId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronTramplingEffectData> GetAllTramplingEffects() const;

    // Collision optimization
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void OptimizeCollisionForDistance(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void SetCollisionLOD(const FString& FoliageTypeId, int32 LODLevel, bool bEnableCollision);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void UpdateCollisionBasedOnLOD(const FString& FoliageTypeId, int32 CurrentLOD);

    // Collision queries
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FString> GetFoliageInstancesInRadius(const FVector& Center, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FString> GetFoliageInstancesInBox(const FBox& Box) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool LineTraceAgainstFoliage(const FVector& Start, const FVector& End, FHitResult& OutHitResult) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool SphereTraceAgainstFoliage(const FVector& Start, const FVector& End, float Radius, FHitResult& OutHitResult) const;

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronCollisionPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    int32 GetActiveCollisionCount() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    int32 GetDestructibleCount() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void DrawDebugCollisionMeshes(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void LogCollisionStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCollisionMeshGenerated, FString, CollisionMeshId, FAuracronCollisionMeshData, MeshData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFoliageDestroyed, FString, DestructibleId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTramplingEffectCreated, FString, TramplingId, FAuracronTramplingEffectData, TramplingData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPhysicsInteraction, FString, FoliageInstanceId);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCollisionMeshGenerated OnCollisionMeshGenerated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageDestroyed OnFoliageDestroyed;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTramplingEffectCreated OnTramplingEffectCreated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPhysicsInteraction OnPhysicsInteraction;

private:
    static UAuracronFoliageCollisionManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronFoliageCollisionConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Collision data
    TMap<FString, FAuracronCollisionMeshData> CollisionMeshes;
    TMap<FString, FAuracronCollisionMeshData> CollisionMeshData;
    TMap<FString, FAuracronDestructibleFoliageData> DestructibleFoliage;
    TMap<FString, FAuracronDestructibleData> DestructibleData;
    TMap<FString, FAuracronTramplingEffectData> TramplingEffects;
    TMap<FString, EAuracronPhysicsInteractionType> FoliagePhysicsTypes;
    TMap<FString, FAuracronPhysicsProperties> PhysicsProperties;

    // Performance data
    FAuracronCollisionPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastCollisionUpdate = 0.0f;
    float LastPhysicsUpdate = 0.0f;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection CollisionLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateCollisionMeshId() const;
    FString GenerateDestructibleId() const;
    FString GenerateTramplingId() const;
    void UpdateCollisionMeshInternal(FAuracronCollisionMeshData& MeshData, float DeltaTime);
    void UpdateDestructibleFoliageInternal(FAuracronDestructibleFoliageData& DestructibleData, float DeltaTime);
    void UpdateTramplingEffectInternal(FAuracronTramplingEffectData& TramplingData, float DeltaTime);
    void UpdatePerformanceDataInternal();
    UStaticMesh* GenerateSimplifiedCollisionMesh(UStaticMesh* SourceMesh, float ComplexityLevel) const;
    void ApplyPhysicsPropertiesInternal(FBodyInstance* BodyInstance, const FAuracronCollisionMeshData& CollisionData);
    void CreateDestructionFragments(const FVector& Location, const FAuracronDestructibleFoliageData& DestructibleData);
    void ApplyTramplingDeformation(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronTramplingEffectData& TramplingData);

    // Additional internal functions for collision processing
    float CalculateRealCollisionMemoryUsage();
    bool ApplyEdgeCollapseForCollision(FMeshDescription* MeshDescription, float ReductionPercentage) const;
    bool ApplyConvexHullSimplification(FMeshDescription* MeshDescription, float ReductionPercentage) const;
    bool ApplyBoundingBoxCollision(UStaticMesh* Mesh) const;
    bool ApplyBoundingBoxSimplification(FMeshDescription* MeshDescription, float ReductionPercentage) const;
    void BuildBoxMeshDescription(FMeshDescription* MeshDescription, const TArray<FVector>& BoxVertices, const TArray<int32>& BoxIndices) const;
};
