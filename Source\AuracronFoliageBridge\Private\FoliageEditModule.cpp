// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON Foliage Edit Module Implementation
// UE 5.6 Compatible Implementation

#include "FoliageEditModule.h"
#include "AuracronFoliageTypes.h"
#include "AuracronFoliage.h"
#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

#if WITH_EDITOR
#include "FoliageEditUtility.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Toolkits/ToolkitManager.h"
#endif

DEFINE_LOG_CATEGORY_STATIC(LogAuracronFoliageEdit, Log, All);

/**
 * Foliage Edit Module Implementation
 */
class FAuracronFoliageEditModule : public IModuleInterface
{
public:
    virtual void StartupModule() override
    {
        UE_LOG(LogAuracronFoliageEdit, Log, TEXT("AuracronFoliageEdit module started"));
    }

    virtual void ShutdownModule() override
    {
        UE_LOG(LogAuracronFoliageEdit, Log, TEXT("AuracronFoliageEdit module shutdown"));
    }
};

IMPLEMENT_MODULE(FAuracronFoliageEditModule, AuracronFoliageBridge)
