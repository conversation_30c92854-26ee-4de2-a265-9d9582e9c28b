// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Seasonal Changes System Header
// Bridge 4.8: Foliage - Seasonal Changes

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageLOD.h"
#include "AuracronFoliageWind.h"
#include "AuracronFoliageCollision.h"

// UE5.6 Material includes
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageType.h"
#include "FoliageInstancedStaticMeshComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "Engine/Texture2D.h"

// Math and Utilities
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"
#include "Misc/DateTime.h"

// Curve includes
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"

#include "AuracronFoliageSeasonal.generated.h"

// Forward declarations
class UAuracronFoliageSeasonalManager;
class UAuracronFoliageBiomeManager;

// =============================================================================
// SEASONAL TYPES AND ENUMS
// =============================================================================

// Season types
UENUM(BlueprintType)
enum class EAuracronSeasonType : uint8
{
    Spring                  UMETA(DisplayName = "Spring"),
    Summer                  UMETA(DisplayName = "Summer"),
    Autumn                  UMETA(DisplayName = "Autumn"),
    Winter                  UMETA(DisplayName = "Winter"),
    WetSeason               UMETA(DisplayName = "Wet Season"),
    DrySeason               UMETA(DisplayName = "Dry Season"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Growth phases
UENUM(BlueprintType)
enum class EAuracronGrowthPhase : uint8
{
    Seed                    UMETA(DisplayName = "Seed"),
    Sprout                  UMETA(DisplayName = "Sprout"),
    Juvenile                UMETA(DisplayName = "Juvenile"),
    Mature                  UMETA(DisplayName = "Mature"),
    Flowering               UMETA(DisplayName = "Flowering"),
    Fruiting                UMETA(DisplayName = "Fruiting"),
    Senescent               UMETA(DisplayName = "Senescent"),
    Dormant                 UMETA(DisplayName = "Dormant"),
    Dead                    UMETA(DisplayName = "Dead")
};

// Seasonal change types
UENUM(BlueprintType)
enum class EAuracronSeasonalChangeType : uint8
{
    ColorVariation          UMETA(DisplayName = "Color Variation"),
    DensityChange           UMETA(DisplayName = "Density Change"),
    GrowthSimulation        UMETA(DisplayName = "Growth Simulation"),
    LifecycleManagement     UMETA(DisplayName = "Lifecycle Management"),
    MaterialTransition      UMETA(DisplayName = "Material Transition"),
    ScaleVariation          UMETA(DisplayName = "Scale Variation"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Lifecycle events
UENUM(BlueprintType)
enum class EAuracronLifecycleEvent : uint8
{
    Germination             UMETA(DisplayName = "Germination"),
    LeafBudding             UMETA(DisplayName = "Leaf Budding"),
    Flowering               UMETA(DisplayName = "Flowering"),
    Fruiting                UMETA(DisplayName = "Fruiting"),
    LeafSenescence          UMETA(DisplayName = "Leaf Senescence"),
    LeafDrop                UMETA(DisplayName = "Leaf Drop"),
    Dormancy                UMETA(DisplayName = "Dormancy"),
    Death                   UMETA(DisplayName = "Death"),
    Regeneration            UMETA(DisplayName = "Regeneration")
};

// =============================================================================
// SEASONAL CONFIGURATION DATA
// =============================================================================

/**
 * Seasonal Configuration Data
 * Configuration for seasonal changes system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronSeasonalConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal System")
    bool bEnableSeasonalChanges = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal System")
    EAuracronSeasonType DefaultSeason = EAuracronSeasonType::Spring;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Time System")
    float SeasonDurationDays = 90.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Time System")
    float DayLengthSeconds = 1200.0f; // 20 minutes real time = 1 day game time

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Time System")
    float TimeAcceleration = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    bool bEnableColorVariation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ColorTransitionSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ColorVariationIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Density Changes")
    bool bEnableDensityChanges = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Density Changes")
    float DensityChangeSpeed = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Density Changes")
    float MaxDensityVariation = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    bool bEnableGrowthSimulation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    float GrowthRate = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    float MaxGrowthScale = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle Management")
    bool bEnableLifecycleManagement = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle Management")
    float LifecycleSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle Management")
    bool bAutoRegeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncSeasonalUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxSeasonalUpdatesPerFrame = 200;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float SeasonalUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxSeasonalDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    TSoftObjectPtr<UMaterialParameterCollection> SeasonalParameterCollection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString SeasonProgressParameterName = TEXT("SeasonProgress");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString SeasonTypeParameterName = TEXT("SeasonType");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString GrowthPhaseParameterName = TEXT("GrowthPhase");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameters")
    FString LifecycleProgressParameterName = TEXT("LifecycleProgress");

    FAuracronSeasonalConfiguration()
    {
        bEnableSeasonalChanges = true;
        DefaultSeason = EAuracronSeasonType::Spring;
        SeasonDurationDays = 90.0f;
        DayLengthSeconds = 1200.0f;
        TimeAcceleration = 1.0f;
        bEnableColorVariation = true;
        ColorTransitionSpeed = 1.0f;
        ColorVariationIntensity = 1.0f;
        bEnableDensityChanges = true;
        DensityChangeSpeed = 0.5f;
        MaxDensityVariation = 0.3f;
        bEnableGrowthSimulation = true;
        GrowthRate = 1.0f;
        MaxGrowthScale = 2.0f;
        bEnableLifecycleManagement = true;
        LifecycleSpeed = 1.0f;
        bAutoRegeneration = true;
        bEnableAsyncSeasonalUpdates = true;
        MaxSeasonalUpdatesPerFrame = 200;
        SeasonalUpdateInterval = 0.1f;
        MaxSeasonalDistance = 15000.0f;
        SeasonProgressParameterName = TEXT("SeasonProgress");
        SeasonTypeParameterName = TEXT("SeasonType");
        GrowthPhaseParameterName = TEXT("GrowthPhase");
        LifecycleProgressParameterName = TEXT("LifecycleProgress");
    }
};

// =============================================================================
// SEASONAL COLOR DATA
// =============================================================================

/**
 * Seasonal Color Data
 * Color variations for different seasons
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronSeasonalColorData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal Colors")
    FString ColorDataId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spring Colors")
    FLinearColor SpringBaseColor = FLinearColor(0.2f, 0.8f, 0.2f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spring Colors")
    FLinearColor SpringAccentColor = FLinearColor(0.8f, 1.0f, 0.3f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Summer Colors")
    FLinearColor SummerBaseColor = FLinearColor(0.1f, 0.6f, 0.1f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Summer Colors")
    FLinearColor SummerAccentColor = FLinearColor(0.0f, 0.8f, 0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Autumn Colors")
    FLinearColor AutumnBaseColor = FLinearColor(0.8f, 0.4f, 0.1f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Autumn Colors")
    FLinearColor AutumnAccentColor = FLinearColor(1.0f, 0.6f, 0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Winter Colors")
    FLinearColor WinterBaseColor = FLinearColor(0.3f, 0.3f, 0.2f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Winter Colors")
    FLinearColor WinterAccentColor = FLinearColor(0.5f, 0.5f, 0.4f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Curves")
    TSoftObjectPtr<UCurveLinearColor> ColorTransitionCurve;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ColorVariationRange = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ColorSaturationMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ColorBrightnessMultiplier = 1.0f;

    FAuracronSeasonalColorData()
    {
        SpringBaseColor = FLinearColor(0.2f, 0.8f, 0.2f, 1.0f);
        SpringAccentColor = FLinearColor(0.8f, 1.0f, 0.3f, 1.0f);
        SummerBaseColor = FLinearColor(0.1f, 0.6f, 0.1f, 1.0f);
        SummerAccentColor = FLinearColor(0.0f, 0.8f, 0.0f, 1.0f);
        AutumnBaseColor = FLinearColor(0.8f, 0.4f, 0.1f, 1.0f);
        AutumnAccentColor = FLinearColor(1.0f, 0.6f, 0.0f, 1.0f);
        WinterBaseColor = FLinearColor(0.3f, 0.3f, 0.2f, 1.0f);
        WinterAccentColor = FLinearColor(0.5f, 0.5f, 0.4f, 1.0f);
        ColorVariationRange = 0.2f;
        ColorSaturationMultiplier = 1.0f;
        ColorBrightnessMultiplier = 1.0f;
    }
};

// =============================================================================
// GROWTH SIMULATION DATA
// =============================================================================

/**
 * Growth Simulation Data
 * Data for foliage growth simulation
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronGrowthSimulationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    FString GrowthDataId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    EAuracronGrowthPhase CurrentPhase = EAuracronGrowthPhase::Seed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    float GrowthProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    float GrowthRate = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    float MaxScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Simulation")
    float CurrentScale = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Phases")
    TMap<EAuracronGrowthPhase, float> PhaseDurations;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Phases")
    TMap<EAuracronGrowthPhase, float> PhaseScales;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Growth Curves")
    TSoftObjectPtr<UCurveFloat> GrowthCurve;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Factors")
    float TemperatureInfluence = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Factors")
    float MoistureInfluence = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Factors")
    float LightInfluence = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Factors")
    float NutrientInfluence = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsGrowing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime GrowthStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float LastGrowthUpdate = 0.0f;

    FAuracronGrowthSimulationData()
    {
        CurrentPhase = EAuracronGrowthPhase::Seed;
        GrowthProgress = 0.0f;
        GrowthRate = 1.0f;
        MaxScale = 1.0f;
        CurrentScale = 0.1f;
        TemperatureInfluence = 1.0f;
        MoistureInfluence = 1.0f;
        LightInfluence = 1.0f;
        NutrientInfluence = 1.0f;
        bIsGrowing = true;
        GrowthStartTime = FDateTime::Now();
        LastGrowthUpdate = 0.0f;

        // Initialize default phase durations (in days)
        PhaseDurations.Add(EAuracronGrowthPhase::Seed, 7.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Sprout, 14.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Juvenile, 30.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Mature, 60.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Flowering, 21.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Fruiting, 30.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Senescent, 45.0f);
        PhaseDurations.Add(EAuracronGrowthPhase::Dormant, 90.0f);

        // Initialize default phase scales
        PhaseScales.Add(EAuracronGrowthPhase::Seed, 0.1f);
        PhaseScales.Add(EAuracronGrowthPhase::Sprout, 0.2f);
        PhaseScales.Add(EAuracronGrowthPhase::Juvenile, 0.5f);
        PhaseScales.Add(EAuracronGrowthPhase::Mature, 1.0f);
        PhaseScales.Add(EAuracronGrowthPhase::Flowering, 1.1f);
        PhaseScales.Add(EAuracronGrowthPhase::Fruiting, 1.0f);
        PhaseScales.Add(EAuracronGrowthPhase::Senescent, 0.8f);
        PhaseScales.Add(EAuracronGrowthPhase::Dormant, 0.6f);
    }
};

// =============================================================================
// LIFECYCLE MANAGEMENT DATA
// =============================================================================

/**
 * Lifecycle Management Data
 * Data for foliage lifecycle management
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronLifecycleData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    FString LifecycleId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float LifecycleProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float TotalLifespan = 365.0f; // days

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float CurrentAge = 0.0f; // days

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle Events")
    TArray<EAuracronLifecycleEvent> CompletedEvents;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle Events")
    TArray<EAuracronLifecycleEvent> PendingEvents;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle Events")
    TMap<EAuracronLifecycleEvent, float> EventTriggerAges;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    bool bCanRegenerate = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationChance = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationDelay = 30.0f; // days

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    float Health = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    float MaxHealth = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    float HealthDecayRate = 0.1f; // per day

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsAlive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime BirthTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime LastLifecycleUpdate;

    FAuracronLifecycleData()
    {
        LifecycleProgress = 0.0f;
        TotalLifespan = 365.0f;
        CurrentAge = 0.0f;
        bCanRegenerate = true;
        RegenerationChance = 0.8f;
        RegenerationDelay = 30.0f;
        Health = 100.0f;
        MaxHealth = 100.0f;
        HealthDecayRate = 0.1f;
        bIsAlive = true;
        BirthTime = FDateTime::Now();
        LastLifecycleUpdate = FDateTime::Now();

        // Initialize default event trigger ages (in days)
        EventTriggerAges.Add(EAuracronLifecycleEvent::Germination, 0.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::LeafBudding, 14.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::Flowering, 120.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::Fruiting, 150.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::LeafSenescence, 300.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::LeafDrop, 330.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::Dormancy, 350.0f);
        EventTriggerAges.Add(EAuracronLifecycleEvent::Death, 365.0f);

        // Initialize pending events
        PendingEvents.Add(EAuracronLifecycleEvent::Germination);
        PendingEvents.Add(EAuracronLifecycleEvent::LeafBudding);
        PendingEvents.Add(EAuracronLifecycleEvent::Flowering);
        PendingEvents.Add(EAuracronLifecycleEvent::Fruiting);
        PendingEvents.Add(EAuracronLifecycleEvent::LeafSenescence);
        PendingEvents.Add(EAuracronLifecycleEvent::LeafDrop);
        PendingEvents.Add(EAuracronLifecycleEvent::Dormancy);
    }
};

// =============================================================================
// SEASONAL PERFORMANCE DATA
// =============================================================================

/**
 * Seasonal Performance Data
 * Performance metrics for seasonal system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronSeasonalPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalSeasonalInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ActiveSeasonalInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 GrowingInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 LifecycleInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float SeasonalUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float ColorUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float GrowthUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LifecycleUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaterialParameterUpdates = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    FAuracronSeasonalPerformanceData()
    {
        TotalSeasonalInstances = 0;
        ActiveSeasonalInstances = 0;
        GrowingInstances = 0;
        LifecycleInstances = 0;
        SeasonalUpdateTime = 0.0f;
        ColorUpdateTime = 0.0f;
        GrowthUpdateTime = 0.0f;
        LifecycleUpdateTime = 0.0f;
        MaterialParameterUpdates = 0;
        MemoryUsageMB = 0.0f;
    }
};

// =============================================================================
// FOLIAGE SEASONAL MANAGER
// =============================================================================

/**
 * Foliage Seasonal Manager
 * Manager for the foliage seasonal changes system including color variation, growth simulation, and lifecycle management
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageSeasonalManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    static UAuracronFoliageSeasonalManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void Initialize(const FAuracronSeasonalConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SetConfiguration(const FAuracronSeasonalConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FAuracronSeasonalConfiguration GetConfiguration() const;

    // Season control
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SetCurrentSeason(EAuracronSeasonType Season);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    EAuracronSeasonType GetCurrentSeason() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    float GetSeasonProgress() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SetSeasonProgress(float Progress);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void AdvanceSeasonByDays(float Days);

    // Time system
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SetTimeAcceleration(float Acceleration);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    float GetTimeAcceleration() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    float GetCurrentGameTime() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    int32 GetCurrentGameDay() const;

    // Color variation
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SetSeasonalColors(const FString& FoliageTypeId, const FAuracronSeasonalColorData& ColorData);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FAuracronSeasonalColorData GetSeasonalColors(const FString& FoliageTypeId) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FLinearColor GetCurrentSeasonalColor(const FString& FoliageTypeId, bool bUseAccentColor = false) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FLinearColor GetSeasonalColor(EAuracronSeasonType Season, const FLinearColor& BaseColor) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FLinearColor GetSeasonalTint(EAuracronSeasonType Season) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FLinearColor GetCurrentSeasonalColorFromData(const FAuracronSeasonalColorData& ColorData) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FLinearColor GetCurrentSeasonalTint() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void UpdateColorVariation(float DeltaTime);

    // Growth simulation
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FString CreateGrowthSimulation(const FString& FoliageInstanceId, const FAuracronGrowthSimulationData& GrowthData);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    bool UpdateGrowthSimulation(const FString& GrowthId, const FAuracronGrowthSimulationData& GrowthData);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    bool RemoveGrowthSimulation(const FString& GrowthId);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FAuracronGrowthSimulationData GetGrowthSimulation(const FString& GrowthId) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void UpdateGrowthSimulations(float DeltaTime);

    // Lifecycle management
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FString CreateLifecycleManagement(const FString& FoliageInstanceId, const FAuracronLifecycleData& LifecycleData);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    bool UpdateLifecycleManagement(const FString& LifecycleId, const FAuracronLifecycleData& LifecycleData);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    bool RemoveLifecycleManagement(const FString& LifecycleId);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FAuracronLifecycleData GetLifecycleManagement(const FString& LifecycleId) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void UpdateAllLifecycleManagement(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void TriggerLifecycleEvent(const FString& LifecycleId, EAuracronLifecycleEvent Event);

    // Density changes
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void UpdateDensityChanges(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    float GetSeasonalDensityMultiplier(EAuracronSeasonType Season) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void ApplyDensityChangesToBiome(const FString& BiomeId);

    // Material parameter control
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void UpdateMaterialParameters();

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SetMaterialParameterCollection(UMaterialParameterCollection* ParameterCollection);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    UMaterialParameterCollection* GetMaterialParameterCollection() const;

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void IntegrateWithBiomeSystem(UAuracronFoliageBiomeManager* BiomeManager);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void IntegrateWithWindSystem(UAuracronFoliageWindManager* WindManager);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void SynchronizeWithClimateData(const FString& BiomeId);

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    FAuracronSeasonalPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    int32 GetActiveSeasonalInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    int32 GetGrowingInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    float CalculateMemoryUsage() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void DrawDebugSeasonalInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Seasonal Manager")
    void LogSeasonalStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSeasonChanged, EAuracronSeasonType, NewSeason);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGrowthPhaseChanged, FString, GrowthId, EAuracronGrowthPhase, NewPhase);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLifecycleEventTriggered, FString, LifecycleId, EAuracronLifecycleEvent, Event);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnColorVariationUpdated, FString, FoliageTypeId, FLinearColor, NewColor);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSeasonChanged OnSeasonChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnGrowthPhaseChanged OnGrowthPhaseChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLifecycleEventTriggered OnLifecycleEventTriggered;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnColorVariationUpdated OnColorVariationUpdated;

private:
    static UAuracronFoliageSeasonalManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronSeasonalConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Seasonal data
    EAuracronSeasonType CurrentSeason = EAuracronSeasonType::Spring;
    float SeasonProgress = 0.0f;
    float GameTime = 0.0f;
    int32 CurrentGameDay = 0;

    // Color data
    TMap<FString, FAuracronSeasonalColorData> SeasonalColors;

    // Growth simulation data
    TMap<FString, FAuracronGrowthSimulationData> GrowthSimulations;

    // Lifecycle management data
    TMap<FString, FAuracronLifecycleData> LifecycleManagement;

    // Material parameter collection
    UPROPERTY()
    TWeakObjectPtr<UMaterialParameterCollection> SeasonalParameterCollection;

    UPROPERTY()
    TWeakObjectPtr<UMaterialParameterCollectionInstance> SeasonalParameterInstance;

    // Integration with other systems
    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageBiomeManager> BiomeManager;

    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageWindManager> WindManager;

    // Performance data
    FAuracronSeasonalPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastSeasonalUpdate = 0.0f;
    float LastColorUpdate = 0.0f;
    float LastGrowthUpdate = 0.0f;
    float LastLifecycleUpdate = 0.0f;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection SeasonalLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateGrowthId() const;
    FString GenerateLifecycleId() const;
    void UpdateSeasonalProgressInternal(float DeltaTime);
    void UpdateColorVariationInternal(float DeltaTime);
    void UpdateGrowthSimulationInternal(FAuracronGrowthSimulationData& GrowthData, float DeltaTime);
    void UpdateLifecycleManagementInternal(FAuracronLifecycleData& LifecycleData, float DeltaTime);
    void UpdateMaterialParametersInternal();
    void UpdatePerformanceDataInternal();
    FLinearColor CalculateSeasonalColor(const FAuracronSeasonalColorData& ColorData, EAuracronSeasonType Season, float Progress) const;
    float CalculateGrowthRate(const FAuracronGrowthSimulationData& GrowthData) const;
    void AdvanceGrowthPhase(FAuracronGrowthSimulationData& GrowthData);
    void ProcessLifecycleEvents(FAuracronLifecycleData& LifecycleData);
    void ApplySeasonalEffectsToFoliage(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex);
    float GetSeasonalDensityMultiplierInternal(EAuracronSeasonType Season) const;
    float CalculateRealSeasonalMemoryUsage() const;
};
