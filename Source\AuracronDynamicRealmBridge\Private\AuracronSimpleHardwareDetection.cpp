#include "AuracronSimpleHardwareDetection.h"
#include "Engine/Engine.h"
#include "GameFramework/GameUserSettings.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "RHI.h"
#include "RHIGlobals.h"
#include "RHIShaderPlatform.h"
#include "DataDrivenShaderPlatformInfo.h"
#include "RenderCore.h"
#include "GenericPlatform/GenericPlatformMemory.h"

UAuracronSimpleHardwareDetection::UAuracronSimpleHardwareDetection()
{
}

FSimpleHardwareInfo UAuracronSimpleHardwareDetection::DetectHardware()
{
    FSimpleHardwareInfo HardwareInfo;

    // Detectar GPU
    HardwareInfo.GPUName = DetectGPUName();
    HardwareInfo.VideoMemoryMB = DetectVideoMemory();

    // Detectar CPU
    HardwareInfo.CPUCores = DetectCPUCores();

    // Detectar memória
    DetectMemoryInfo(HardwareInfo.TotalRAMGB, HardwareInfo.AvailableRAMGB);

    // Detectar suporte a recursos avançados
    HardwareInfo.bSupportsRayTracing = DetectRayTracingSupport();
    HardwareInfo.bSupportsMeshShaders = DetectMeshShaderSupport();

    return HardwareInfo;
}

FString UAuracronSimpleHardwareDetection::DetectGPUName()
{
    // Usar RHI para obter nome da GPU
    if (GRHIAdapterName.Len() > 0)
    {
        return GRHIAdapterName;
    }
    
    return TEXT("Unknown GPU");
}

int32 UAuracronSimpleHardwareDetection::DetectVideoMemory()
{
    // Usar RHI para obter memória de vídeo
    FTextureMemoryStats TextureMemStats;
    RHIGetTextureMemoryStats(TextureMemStats);
    
    if (TextureMemStats.DedicatedVideoMemory > 0)
    {
        return TextureMemStats.DedicatedVideoMemory / (1024 * 1024); // Converter para MB
    }
    
    // Fallback: estimar baseado no nome da GPU
    FString GPUName = DetectGPUName().ToLower();
    if (GPUName.Contains(TEXT("rtx 4090")))
    {
        return 24576; // 24GB
    }
    else if (GPUName.Contains(TEXT("rtx 4080")))
    {
        return 16384; // 16GB
    }
    else if (GPUName.Contains(TEXT("rtx 4070")))
    {
        return 12288; // 12GB
    }
    else if (GPUName.Contains(TEXT("rtx 3080")))
    {
        return 10240; // 10GB
    }
    else if (GPUName.Contains(TEXT("rtx 3070")))
    {
        return 8192; // 8GB
    }
    else if (GPUName.Contains(TEXT("gtx 1660")))
    {
        return 6144; // 6GB
    }
    
    return 4096; // 4GB default
}

int32 UAuracronSimpleHardwareDetection::DetectCPUCores()
{
    return FPlatformMisc::NumberOfCores();
}

void UAuracronSimpleHardwareDetection::DetectMemoryInfo(float& TotalRAM, float& AvailableRAM)
{
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    
    TotalRAM = MemStats.TotalPhysical / (1024.0f * 1024.0f * 1024.0f); // Converter para GB
    AvailableRAM = MemStats.AvailablePhysical / (1024.0f * 1024.0f * 1024.0f); // Converter para GB
}

bool UAuracronSimpleHardwareDetection::DetectRayTracingSupport()
{
    // Verificar se RHI suporta ray tracing
    return RHISupportsRayTracing(GMaxRHIShaderPlatform);
}

bool UAuracronSimpleHardwareDetection::DetectMeshShaderSupport()
{
    // Verificar se RHI suporta mesh shaders
    return RHISupportsMeshShadersTier1(GMaxRHIShaderPlatform);
}

FSimpleQualitySettings UAuracronSimpleHardwareDetection::CalculateRecommendedSettings(const FSimpleHardwareInfo& HardwareInfo)
{
    FSimpleQualitySettings Settings;
    
    // Calcular nível de qualidade baseado no hardware
    int32 QualityLevel = CalculateQualityLevel(HardwareInfo);
    
    // Aplicar configurações baseadas no nível de qualidade
    Settings.OverallQuality = QualityLevel;
    Settings.TextureQuality = QualityLevel;
    Settings.ShadowQuality = QualityLevel;
    Settings.PostProcessQuality = QualityLevel;
    Settings.AntiAliasingQuality = QualityLevel;
    Settings.ViewDistanceQuality = QualityLevel;
    Settings.FoliageQuality = QualityLevel;
    Settings.ShadingQuality = QualityLevel;
    
    // Configurar resolução baseada na GPU
    if (HardwareInfo.VideoMemoryMB >= 16384) // 16GB+
    {
        Settings.ResolutionScale = 1.0f; // 100%
        Settings.TargetFPS = 60;
    }
    else if (HardwareInfo.VideoMemoryMB >= 8192) // 8GB+
    {
        Settings.ResolutionScale = 0.9f; // 90%
        Settings.TargetFPS = 60;
    }
    else if (HardwareInfo.VideoMemoryMB >= 6144) // 6GB+
    {
        Settings.ResolutionScale = 0.8f; // 80%
        Settings.TargetFPS = 60;
    }
    else
    {
        Settings.ResolutionScale = 0.7f; // 70%
        Settings.TargetFPS = 30;
    }
    
    // Configurar recursos avançados
    Settings.bEnableRayTracing = HardwareInfo.bSupportsRayTracing && HardwareInfo.VideoMemoryMB >= 8192;
    Settings.bEnableLumen = HardwareInfo.VideoMemoryMB >= 6144;
    Settings.bEnableNanite = HardwareInfo.VideoMemoryMB >= 4096;
    
    return Settings;
}

int32 UAuracronSimpleHardwareDetection::CalculateQualityLevel(const FSimpleHardwareInfo& HardwareInfo)
{
    int32 Score = 0;
    
    // Pontuação baseada na memória de vídeo
    if (HardwareInfo.VideoMemoryMB >= 16384) Score += 4; // 16GB+
    else if (HardwareInfo.VideoMemoryMB >= 12288) Score += 3; // 12GB+
    else if (HardwareInfo.VideoMemoryMB >= 8192) Score += 2; // 8GB+
    else if (HardwareInfo.VideoMemoryMB >= 6144) Score += 1; // 6GB+
    
    // Pontuação baseada na RAM
    if (HardwareInfo.TotalRAMGB >= 32.0f) Score += 2;
    else if (HardwareInfo.TotalRAMGB >= 16.0f) Score += 1;
    
    // Pontuação baseada na CPU
    if (HardwareInfo.CPUCores >= 16) Score += 2;
    else if (HardwareInfo.CPUCores >= 8) Score += 1;
    
    // Pontuação baseada em recursos avançados
    if (HardwareInfo.bSupportsRayTracing) Score += 1;
    if (HardwareInfo.bSupportsMeshShaders) Score += 1;
    
    // Converter pontuação para nível de qualidade (0-4)
    if (Score >= 8) return 4; // Epic
    else if (Score >= 6) return 3; // High
    else if (Score >= 4) return 2; // Medium
    else if (Score >= 2) return 1; // Low
    else return 0; // Very Low
}

void UAuracronSimpleHardwareDetection::ApplyQualitySettings(const FSimpleQualitySettings& Settings)
{
    UGameUserSettings* GameSettings = UGameUserSettings::GetGameUserSettings();
    if (!GameSettings)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to get GameUserSettings"));
        return;
    }
    
    // Aplicar configurações de qualidade
    GameSettings->SetOverallScalabilityLevel(Settings.OverallQuality);
    GameSettings->SetTextureQuality(Settings.TextureQuality);
    GameSettings->SetShadowQuality(Settings.ShadowQuality);
    GameSettings->SetPostProcessingQuality(Settings.PostProcessQuality);
    GameSettings->SetAntiAliasingQuality(Settings.AntiAliasingQuality);
    GameSettings->SetViewDistanceQuality(Settings.ViewDistanceQuality);
    GameSettings->SetFoliageQuality(Settings.FoliageQuality);
    GameSettings->SetShadingQuality(Settings.ShadingQuality);
    
    // Aplicar escala de resolução
    GameSettings->SetResolutionScaleValueEx(Settings.ResolutionScale);
    
    // Aplicar limite de FPS
    GameSettings->SetFrameRateLimit(Settings.TargetFPS);
    
    // Salvar e aplicar configurações
    GameSettings->ApplySettings(false);
    GameSettings->SaveSettings();
    
    UE_LOG(LogTemp, Log, TEXT("Applied quality settings: Overall=%d, Resolution=%.2f, FPS=%d"), 
           Settings.OverallQuality, Settings.ResolutionScale, Settings.TargetFPS);
}

void UAuracronSimpleHardwareDetection::AutoDetectAndApplySettings()
{
    // Detectar hardware
    FSimpleHardwareInfo HardwareInfo = DetectHardware();
    
    UE_LOG(LogTemp, Log, TEXT("Detected Hardware: GPU=%s, VRAM=%dMB, RAM=%.1fGB, Cores=%d"), 
           *HardwareInfo.GPUName, HardwareInfo.VideoMemoryMB, HardwareInfo.TotalRAMGB, HardwareInfo.CPUCores);
    
    // Calcular configurações recomendadas
    FSimpleQualitySettings RecommendedSettings = CalculateRecommendedSettings(HardwareInfo);
    
    // Aplicar configurações
    ApplyQualitySettings(RecommendedSettings);
}
