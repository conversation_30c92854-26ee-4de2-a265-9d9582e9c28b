using UnrealBuildTool;

public class AuracronDynamicRealmBridge : ModuleRules
{
    public AuracronDynamicRealmBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Enable latest C++ standard
        CppStandard = CppStandardVersion.Latest;
        bEnableExceptions = true;
        
        PublicIncludePaths.AddRange(
            new string[] {
                "AuracronDynamicRealmBridge/Public"
            }
        );
        
        PrivateIncludePaths.AddRange(
            new string[] {
                "AuracronDynamicRealmBridge/Private"
            }
        );
        
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "InputCore",
                "Chaos",
                "PhysicsCore",
                "GeometryCollectionEngine",
                "FieldSystemEngine",
                "ChaosSolverEngine",
                "ChaosVehicles",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "AIModule",
                "NavigationSystem",
                "UMG",
                "CommonUI",
                "CommonInput",
                "EnhancedInput",
                "Niagara",
                "NiagaraCore",
                "NiagaraShader",
                "CinematicCamera",
                "LevelSequence",
                "MovieScene",
                "MovieSceneTracks",
                "ActorSequence",
                "SequencerScripting",
                "Landscape",
                "Foliage",
                "ProceduralMeshComponent",
                "GeometryScriptingCore",
                "ModelingComponents",
                "MeshDescription",
                "StaticMeshDescription",
                "DynamicMesh",
                "GeometryCore",
                "InteractiveToolsFramework",
                "MeshModelingTools",
                "MeshModelingToolsExp",
                "ModelingOperators",

                "DynamicMesh",
                "MeshConversion",
                "MeshUtilitiesCommon",
                "Projects",
                "SandboxFile",
                "AppFramework",



                "ApplicationCore",
                "Json",

                "HTTP",
                "HTTPServer",
                "Sockets",
                "Networking",
                "PacketHandler",
                "ReliabilityHandlerComponent",
                "NetCore",
                "OnlineSubsystem",
                "OnlineSubsystemUtils",
                "Steamworks",
                "Voice",
                "AudioMixer",
                "AudioExtensions",
                "SignalProcessing",

                "AudioModulation",
                "MetasoundEngine",
                "MetasoundFrontend",
                "MetasoundStandardNodes",
                "MetasoundGenerator",
                "WaveTable",
                "Synthesis",
                "AudioSynesthesia",
                "AudioWidgets",
                "AudioAnalyzer",




                "PCG",
                "Auracron",
                "AuracronSigilosBridge"
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "ApplicationCore",
                "AppFramework",
                "AssetRegistry",

                "Projects",
                "MeshDescription",
                "StaticMeshDescription",
                "SkeletalMeshDescription",
                "MeshUtilitiesCommon",
                "ClothingSystemRuntimeCommon",
                "ClothingSystemRuntimeInterface",
                "ClothingSystemRuntimeNv",
                "TimeManagement",









                "FunctionalTesting",


                "ApplicationCore",


                "Engine",
                "EngineSettings",

                "HeadMountedDisplay",
                "XRBase",
                "AugmentedReality",
                "LocationServicesBPLibrary",
                "MobilePatchingUtils",
                "BuildPatchServices",
                "PakFile",
                "RSA",
                "SSL",
                "libWebSockets",
                "OpenSSL",
                "zlib",
                "UElibPNG",
                "UElibJPG",
                "FreeType2",
                "HarfBuzz",
                "ICU",
                "OnlineSubsystem",
                "OnlineSubsystemUtils",
                "Steamworks",
                "Voice",

                "AudioMixer",
                "AudioMixerCore",


                "AudioCaptureCore",
                "AudioCapture",
                "AudioCaptureRtAudio",

                "UEOgg",
                "Vorbis",
                "VorbisFile",



            }
        );
        
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        
        // Platform specific settings
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_DYNAMIC_REALM_PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Linux)
        {
            PublicDefinitions.Add("AURACRON_DYNAMIC_REALM_PLATFORM_LINUX=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        {
            PublicDefinitions.Add("AURACRON_DYNAMIC_REALM_PLATFORM_MAC=1");
        }
        
        // Feature flags
        PublicDefinitions.Add("WITH_AURACRON_DYNAMIC_REALM=1");
        PublicDefinitions.Add("WITH_AURACRON_THREE_LAYERS=1");
        PublicDefinitions.Add("WITH_AURACRON_VERTICAL_TRANSITIONS=1");
        PublicDefinitions.Add("WITH_AURACRON_PROCEDURAL_GENERATION=1");
        PublicDefinitions.Add("WITH_AURACRON_WORLD_PARTITION=1");
        
        // Performance optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_DYNAMIC_REALM_OPTIMIZED=1");
            PublicDefinitions.Add("AURACRON_DISABLE_DEBUG_FEATURES=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_DYNAMIC_REALM_DEBUG=1");
            PublicDefinitions.Add("AURACRON_ENABLE_DEBUG_FEATURES=1");
        }
    }
}
